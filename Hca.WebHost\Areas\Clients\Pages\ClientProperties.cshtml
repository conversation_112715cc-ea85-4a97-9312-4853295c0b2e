﻿@page "/clients/{clientId:guid}/properties"
@model Hca.WebHost.Areas.Clients.Pages.ClientPropertiesModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="content-area">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/properties"
                  hx-target="#divPropertySearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, change from:#ShowArchivedProperties, click from:#btnPropertiesSearch">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search properties" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary" type="button" id="btnPropertiesSearch">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            &nbsp;&nbsp;&nbsp;
                            <select asp-for="ComplianceStatusFilter">
                                <option value="0">Show All</option>
                                <option value="1">Compliant</option>
                                <option value="2">Not compliant</option>
                                <option value="3">Next inspection is due within one month</option>
                                <option value="4">No documents uploaded and/or no ACMs identified or presumed</option>
                            </select>
                            &nbsp;&nbsp;&nbsp;
                            <input asp-for="ShowArchivedProperties" class="ml-4 mx-2" />Show Archived Properties
                        </div>
                    </div>
                    @Html.AntiForgeryToken()
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card card-default results-card">
                <div class="card-header">
                    <small class="text-muted">PROPERTIES</small>
                </div>
                <div class="card-body" id="divPropertySearchResults">
                </div>
            </div>
        </div>
    </div>
</div>



@section Styles {
    <style>
        /* Custom layout for content that needs to fill height with proper padding */
        .layout-content-fill .wrapper .section-container {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
            margin-bottom: 0 !important; /* Override the default footer margin */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            /* Keep the normal padding for readability */
        }

        /* Make the container-fluid fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Ensure the main content area fills available space */
        .layout-content-fill .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the results card fill remaining space */
        .layout-content-fill .results-card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .results-card .card-body {
            flex: 1;
            overflow: hidden; /* Prevent the card body itself from scrolling */
            display: flex;
            flex-direction: column;
        }

        /* Style the dynamically loaded content to fill height */
        .layout-content-fill .results-card .card-body > .row {
            flex: 1;
            display: flex;
            margin: 0; /* Remove default row margins */
        }

        .layout-content-fill .results-card .card-body > .row > .col-lg-6 {
            display: flex;
            flex-direction: column;
        }

        /* Make the property list column fill available height */
        .layout-content-fill .results-card .card-body > .row > .col-lg-6:first-child {
            overflow-y: auto; /* Allow scrolling in the property list column */
            max-height: 100%; /* Ensure it doesn't exceed container */
        }

        /* Override the fixed height on the property list table container */
        .layout-content-fill .property-list-table {
            max-height: calc(100vh - 320px) !important; /* Use most of viewport minus header/search/padding/footer */
            height: calc(100vh - 320px); /* Set a specific height to fill space */
            overflow-y: auto;
            border: 1px solid #dee2e6; /* Add border for visual clarity */
        }

        /* Ensure the property list column uses available height */
        .layout-content-fill .results-card .card-body .col-lg-6:first-child {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 320px);
        }

        /* Make sure archived properties section doesn't interfere */
        .layout-content-fill .results-card .card-body .col-lg-6:first-child > br,
        .layout-content-fill .results-card .card-body .col-lg-6:first-child > small {
            flex-shrink: 0; /* Don't let these elements shrink */
        }

        /* Ensure the map column maintains its size */
        .layout-content-fill .results-card .card-body > .row > .col-lg-6:last-child {
            min-height: 400px; /* Minimum height for the map */
        }
    </style>
}

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Properties' }]);
        });

    </script>
}
