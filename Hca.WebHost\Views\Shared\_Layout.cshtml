﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="description" content="Bootstrap Admin App">
    <meta name="keywords" content="app, responsive, jquery, bootstrap, dashboard, admin">
    <title>@ViewData["Title"] - Bootstrap Admin Template</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Place styles for plugins -->
    @if (IsSectionDefined("Styles"))
        {@RenderSection("Styles", required: false)}

    <environment names="Development">
        <!-- =============== VENDOR STYLES ===============-->
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/brands.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/regular.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/solid.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/fontawesome.css' rel="stylesheet" />
        <link href="~/vendor/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" />
        <link href="~/vendor/animate.css/animate.css" rel="stylesheet" />
        <link href="~/vendor/whirl/dist/whirl.css" rel="stylesheet" />
        <!-- =============== BOOTSTRAP STYLES ===============-->
        <link href="~/css/bootstrap.css" rel="stylesheet" id="bscss" />
        <!-- =============== APP STYLES ===============-->
        <link href="~/css/app.css" rel="stylesheet" asp-append-version="true" id="maincss" />
    </environment>
    <environment names="Staging,Production">
        <!-- =============== VENDOR STYLES ===============-->
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/brands.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/regular.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/solid.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/fontawesome.css' rel="stylesheet" />
        <link href="~/vendor/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" />
        <link href="~/vendor/animate.css/animate.css" rel="stylesheet" />
        <link href="~/vendor/whirl/dist/whirl.css" rel="stylesheet" />
        <!-- =============== BOOTSTRAP STYLES ===============-->
        <link href="~/css/bootstrap.css" rel="stylesheet" id="bscss" />
        <!-- =============== APP STYLES ===============-->
        <link href="~/css/app.css" rel="stylesheet" asp-append-version="true" id="maincss" />
    </environment>

</head>
<body>
    <div class="wrapper">
        <!-- top navbar-->
        <header class="topnavbar-wrapper">
            <partial name="_TopNavbar" />
        </header>
        <!-- sidebar-->
        <aside class="aside-container">
            <partial name="_Sidebar" />
        </aside>
        <!-- offsidebar-->
        <aside class="offsidebar d-none">
            <partial name="_Offsidebar" />
        </aside>
        <!-- Main section-->
        <section class="section-container">
            <!-- Page content-->
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </section>
        <!-- Page footer-->
        <footer class="footer-container">
            <partial name="_Footer" />
        </footer>
    </div>

    @if (IsSectionDefined("BodyArea")) {
        @RenderSection("BodyArea", required: false)
    }

    <environment names="Development">

        <!-- =============== VENDOR SCRIPTS ===============-->

        <script src="~/vendor/modernizr/modernizr.custom.js"></script>
        <script src="~/vendor/js-storage/js.storage.js"></script>
        <script src="~/vendor/screenfull/dist/screenfull.js"></script>
        <script src="~/vendor/i18next/i18next.js"></script>
        <script src="~/vendor/i18next-xhr-backend/i18nextXHRBackend.js"></script>
        <!--
        <script src="~/vendor/jquery/dist/jquery.js"></script>
        <script src="~/vendor/popper.js/dist/umd/popper.js"></script>
        <script src="~/vendor/bootstrap/dist/js/bootstrap.js"></script>
        -->

        <!-- =============== PAGE VENDOR SCRIPTS ===============-->
        @RenderSection("scripts", required: false)

        <!-- =============== APP SCRIPTS ===============-->

        <!-- Wrapper -->
        <script src="~/master/js/modules/common/wrapper.js"></script>
        <!-- App init -->
        <script src="~/master/js/app.init.js"></script>
        <!-- Modules -->
        <script src="~/master/js/modules/common/bootstrap-start.js"></script>
        <script src="~/master/js/modules/common/card-tools.js"></script>
        <script src="~/master/js/modules/common/constants.js"></script>
        <script src="~/master/js/modules/common/fullscreen.js"></script>
        <script src="~/master/js/modules/common/load-css.js"></script>
        <script src="~/master/js/modules/common/localize.js"></script>
        <script src="~/master/js/modules/common/navbar-search.js"></script>
        <script src="~/master/js/modules/common/now.js"></script>
        <script src="~/master/js/modules/common/rtl.js"></script>
        <script src="~/master/js/modules/common/sidebar.js"></script>
        <script src="~/master/js/modules/common/slimscroll.js"></script>
        <script src="~/master/js/modules/common/table-checkall.js"></script>
        <script src="~/master/js/modules/common/toggle-state.js"></script>
        <script src="~/master/js/modules/common/trigger-resize.js"></script>
        <!-- Custom -->
        <script src="~/master/js/custom/custom.js"></script>

    </environment>

    <environment names="Staging,Production">

        <!-- =============== VENDOR SCRIPTS ===============-->

        <script src="~/vendor/modernizr/modernizr.custom.js"></script>
        <script src="~/vendor/js-storage/js.storage.js"></script>
        <script src="~/vendor/screenfull/dist/screenfull.js"></script>
        <script src="~/vendor/i18next/i18next.js"></script>
        <script src="~/vendor/i18next-xhr-backend/i18nextXHRBackend.js"></script>
        <!--
        <script src="~/vendor/jquery/dist/jquery.js"></script>
        <script src="~/vendor/popper.js/dist/umd/popper.js"></script>
        <script src="~/vendor/bootstrap/dist/js/bootstrap.js"></script>
        -->

        <!-- =============== PAGE VENDOR SCRIPTS ===============-->
        @RenderSection("scripts", required: false)

        <!-- =============== APP SCRIPTS ===============-->

        <script src="~/js/app.js"></script>

    </environment>



</body>
</html>

