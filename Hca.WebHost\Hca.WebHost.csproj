<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>disable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Areas\Clients\Pages\Quotes\**" />
		<Content Remove="Areas\Clients\Pages\Quotes\**" />
		<EmbeddedResource Remove="Areas\Clients\Pages\Quotes\**" />
		<None Remove="Areas\Clients\Pages\Quotes\**" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Hca.Lib\Hca.Lib.csproj">
			<GlobalPropertiesToRemove></GlobalPropertiesToRemove>
		</ProjectReference>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="6.0.6" />
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="6.0.6" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.20.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Blobs" Version="1.2.1" />
		<PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Keys" Version="1.1.0" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Areas\Clients\Pages\Index\" />
		<Folder Include="Areas\Clients\Pages\Client\" />
		<Folder Include="Areas\Inspections\" />
		<Folder Include="Areas\Inspections\Pages\" />
		<Folder Include="Areas\Inspections\Pages\Search\" />
		<Folder Include="Areas\Inspections\Models\" />
		<Folder Include="wwwroot\master\sass\app\forms\" />
		<Folder Include="Areas\Inspections\Pages\Wizard\" />
		<Folder Include="wwwroot\master\sass\custom\" />
		<Folder Include="Areas\Inspections\Pages\Wizard\ReportFields\" />
		<Folder Include="Areas\Inspections\Pages\Wizard\WizardSteps\" />
		<Folder Include="Identity\" />
		<Folder Include="Areas\Identity\Pages\Shared\" />
		<Folder Include="Services\" />
		<Folder Include="wwwroot\master\js\modules\elements\" />
		<Folder Include="Areas\InspectionValues\" />
		<Folder Include="Areas\InspectionValues\Models\" />
		<Folder Include="Areas\InspectionValues\Pages\" />
		<Folder Include="Areas\InspectionValues\Pages\Widgets\" />
		<Folder Include="Areas\InspectionValues\Pages\Floors\" />
		<Folder Include="Areas\InspectionValues\Pages\Material\" />
		<Folder Include="Areas\InspectionValues\Pages\Quantity\" />
		<Folder Include="Areas\InspectionValues\Pages\Recommendation\" />
		<Folder Include="Areas\InspectionValues\Pages\Priority\" />
		<Folder Include="Areas\Inspections\Pages\Sample\" />
		<Folder Include="Areas\InspectionValues\Pages\FloorPlanColours\" />
		<Folder Include="Areas\Search\" />
		<Folder Include="Areas\Search\Pages\" />
		<Folder Include="wwwroot\master\js\modules\forms\" />
		<Folder Include="Areas\Admin\Pages\Users\" />
		<Folder Include="Pages\ClientView\" />
		<Folder Include="Pages\HcaView\" />
		<Folder Include="Areas\InspectionValues\Pages\InspectionType\" />
		<Folder Include="Models\" />
	</ItemGroup>
	<ItemGroup>
		<Content Remove="Views\Template\_SectionPartial.cshtml" />
		<Content Remove="Views\_ViewImports.cshtml" />
		<Content Remove="Views\_ViewStart.cshtml" />
		<Content Remove="Views\Home\Privacy.cshtml" />
		<Content Remove="Views\Home\Index.cshtml" />
		<Content Remove="Views\SingleView\Index.cshtml" />
		<Content Remove="Views\Shared\_Offsidebar.cshtml" />
		<Content Remove="Views\Shared\_TopNavbarHorizontal.cshtml" />
		<Content Remove="Views\Shared\_LayoutHorizontal.cshtml" />
		<Content Remove="Views\Shared\_Layout.cshtml" />
		<Content Remove="Views\Shared\_TopNavbar.cshtml" />
		<Content Remove="Views\Shared\_LayoutPage.cshtml" />
		<Content Remove="Views\Shared\_Layout_.cshtml" />
		<Content Remove="Views\Shared\_Footer.cshtml" />
		<Content Remove="Views\Shared\Error_.cshtml" />
		<Content Remove="Views\Shared\Error.cshtml" />
		<Content Remove="Views\Shared\_Sidebar.cshtml" />
		<Content Remove="Views\Shared\_ValidationScriptsPartial.cshtml" />
		<Content Remove="Views\Template\Index.cshtml" />
		<Content Remove="Views\Template\_SectionPartial.html" />
		<Content Remove="Pages\Shared\Widgets\ButtonLink.cshtml" />
		<Content Remove="wwwroot\master\js\modules\forms\" />
		<Content Remove="Areas\Clients\Pages\_MapControl.cshtml" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="Areas\Admin\" />
		<None Remove="Areas\Admin\Pages\" />
		<None Remove="Areas\Search\" />
		<None Remove="Areas\Search\Pages\" />
		<None Remove="Areas\Admin\Pages\Users\" />
		<None Remove="Areas\Projects\" />
		<None Remove="Areas\Clients\Pages\Quotes\" />
		<None Remove="Pages\ClientView\" />
		<None Remove="Pages\HcaView\" />
		<None Remove="TagHelpers\" />
		<None Remove="Areas\InspectionValues\Pages\Recommendation\Recommendation\" />
		<None Remove="Areas\InspectionValues\Pages\InspectionTypes\" />
		<None Remove="Azure.Extensions.AspNetCore.DataProtection.Blobs" />
		<None Remove="Azure.Extensions.AspNetCore.DataProtection.Keys" />
		<None Remove="TagHelpers\NavMenu\" />
		<None Remove="TagHelpers\Forms\" />
		<None Remove="Models\" />
	</ItemGroup>
	<Target Name="PrepublishMinification" BeforeTargets="BeforeBuild">
		<Exec Command="gulp $(ConfigurationName)" />
	</Target>
</Project>
