﻿@page "/clients/{clientId:guid}/sites"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitesModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="col-xl-9 col-lg-8">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/sites"
                  hx-target="#divSiteSearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, click from:#btnSearchSites, change from:#ShowArchivedSites">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search sites" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary"
                                type="button"
                                id="btnSearchSites">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            <input asp-for="ShowArchivedSites" class="ml-4 mx-2" />Show Archived Sites
                        </div>
                    </div>

                </div>
                @Html.AntiForgeryToken()
            </form>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">SITES</small>
            </div>
            <div class="card-body" id="divSiteSearchResults">
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Custom layout for content that needs to fill height with proper padding */
        .layout-content-fill .wrapper .section-container {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
            margin-bottom: 0 !important; /* Override the default footer margin */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            /* Keep the normal padding for readability */
        }

        /* Make the container-fluid fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Ensure the main content area fills available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the card fill the available height */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col > .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col > .card > .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    </style>
}

@section scripts{
    <script>

        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Sites' }]);
        });

    </script>
}
