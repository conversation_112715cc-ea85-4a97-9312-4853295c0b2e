﻿@inject Hca.Lib.Services.ClientCountsService ClientCountsService
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    var client = (Hca.Lib.Features.Clients.ClientDto)ViewData["Client"];
    var counts = await ClientCountsService.GetClientCountsAsync(client.Id);

    Layout = "_LayoutHorizontal";
    ViewData["Title"] = client.ClientName;
    ViewData["BodyClass"] = "layout-fs";
}

@section scripts {
    @RenderSection("scripts", required: false)
}

@section Styles {
    @RenderSection("Styles", required: false)
}

@section BodyArea {
    @RenderSection("BodyArea", required: false)
}

<div class="row">
    <div class="col-xl-3 col-lg-4">

        <!-- START menu-->
        <div class="mb-boxes collapse show" id="divClientMenu">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            @if (client.Deleted.HasValue)
                            {
                                <img src="~/images/Deleted.png" style="float: right;" />
                            }
                            else if (client.Archived.HasValue)
                            {
                                <img src="~/images/Archived.png" style="float: right;" />
                            }

                            <small class="text-muted">MENU</small>

                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex align-items-center" href="@Urls.Client(client.Id)">
                                <em class="fa-fw fa-lg fa icon-note mr-2"></em>
                                <span>Details</span>
                            </a>
                        </li>
                        @if (client.ClientType == Lib.Features.Clients.ClientType.Commercial)
                        {
                            <li class="nav-item nav-count">
                                <a class="nav-link d-flex align-items-center" href="@Urls.ClientSites(client.Id)">
                                    <em class="fa-fw fa-lg fa fa-city mr-2"></em>
                                    <span>Sites</span>
                                </a>
                                <span class="menu-item-count">@counts.SiteCount</span>
                            </li>
                            <li class="nav-item nav-count">
                                <a class="nav-link d-flex align-items-center" href="@Urls.ClientProperties(client.Id)">
                                    <em class="fa-fw fa-lg fa fa-building mr-2"></em>
                                    <span>Properties</span>
                                </a>
                                <span class="menu-item-count">@counts.PropertyCount</span>
                            </li>
                            <li class="nav-item nav-count">
                                <a class="nav-link d-flex align-items-center" href="@Urls.ClientContacts(client.Id)">
                                    <em class="fa-fw fa-lg fa fa-user mr-2"></em>
                                    <span>Contacts</span>
                                </a>
                                <span class="menu-item-count">@counts.ContactCount</span>
                            </li>
                        }
                    </ul>
                    @if (client.ArchiveReason.IsPopulated())
                    {
                        <h5 style="font-weight: normal; margin-top: 10px;">
                            <strong>Archive Reason: </strong>
                            @client.ArchiveReason
                        </h5>
                    }
                </div>
            </div>
        </div>

        <div class="collapse show">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            <small class="text-muted">ACTIONS</small>
                        </li>
                        @if (client.Deleted.HasValue)
                        {
                            <nav-link text="Undelete Client" action="@Urls.ClientUndelete(client.Id)"></nav-link>
                        }
                        else if (client.Archived.HasValue && await ViewManager.IsAdminUser)
                        {
                            <nav-link text="Reinstate Client" action="@Urls.ClientReinstate(client.Id)"></nav-link>

                            <nav-delete action="@Urls.ClientDelete(client.Id)"
                                        text="Delete Client"
                                        item-name="client @client.ClientName"></nav-delete>
                        }
                        else
                        {
                            @if (client.ClientType == Lib.Features.Clients.ClientType.Commercial)
                            {
                                <li class="nav-item">
                                    <a href="@Urls.ClientSiteNew(client.Id)" class="nav-link d-flex btn btn-success btn-add-site" style="width: 100%; margin: 5px 0;" id="btnAddSite">New Site</a>
                                </li>
                                <li class="nav-item">
                                    <a href="@Urls.ClientPropertyNew(client.Id)" class="nav-link d-flex btn btn-success btn-add-location" style="width: 100%; margin: 5px 0;" id="btnAddLocation">New Property</a>
                                </li>
                                <li class="nav-item">
                                    <a href="@Urls.ClientContactNew(client.Id)" class="nav-link d-flex btn btn-success btn-add-contact" style="width: 100%; margin: 5px 0;">New Contact</a>
                                </li>
                            }
                            @*
                        <li class="nav-item">
                        <a href="@Urls.ClientQuoteNew(client.Id)" class="nav-link d-flex btn btn-success btn-add-contact" style="width: 100%; margin: 5px 0;">New Quote</a>
                        </li> *@

                            @* <li class="nav-item">
                        <button class="nav-link d-flex btn btn-success" style="width: 100%; margin: 5px 0;" id="btnAddProject">New Project</button>
                        </li> *@

                            <nav-link text="Edit Company Details" action="@Urls.Client(client.Id).AddEditMode()"></nav-link>

                            <nav-link text="Update Company Logo" action="@Urls.ClientLogo(client.Id)"></nav-link>

                            if (await ViewManager.IsAdminUser)
                            {
                                <nav-archive action="@Urls.ClientArchive(client.Id)"
                                             text="Archive Client"
                                             item-name="client @client.ClientName."></nav-archive>
                            }
                        }
                    </ul>
                </div>
            </div>
        </div>
        <!-- END menu-->
    </div>

    <div class="col-xl-9 col-lg-8">

        @RenderBody()

    </div>
</div>

<script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>
<script>
    // navigation
    showSection = (sectionName) => {
        // hide all
        $('#divClientMenu li.nav-item').each((i, e) => $($(e).removeClass('active').data('menuItem')).hide());
        // then show
        $(sectionName).show();
    };

    $('#divClientMenu li.nav-item[data-menu-item]').click((e) => {
        e.preventDefault();
        showSection($(e.currentTarget).addClass('active').data('menuItem'));
    });</script>