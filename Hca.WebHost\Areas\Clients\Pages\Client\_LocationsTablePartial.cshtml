@model IEnumerable<Hca.Lib.Features.Clients.PropertyDtoExtended>
<table class="table table-striped table-bordered table-hover" id="tblProperties">
    <thead class="thead-dark">
        <tr>
            <th style="width: 100%;">Location Name</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var dto in Model)
        {
            <tr>
                <td>@dto.PropertyCode</td>
                <td>
                    <edit action="@Urls.ClientProperty(dto.ClientId, dto.Id)"></edit>
                </td>
            </tr>
        }
    </tbody>
</table>