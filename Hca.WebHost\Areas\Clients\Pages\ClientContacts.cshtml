﻿@page "/clients/{clientId:guid}/contacts"
@model Hca.WebHost.Areas.Clients.Pages.ClientContactsModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="row">
    <div class="col">
        <div class="card card-default">
            <div class="card-body">
                <div class="row" id="divContactFilter" style="display: none;">
                    <div class="col">
                        <div class="form-group mb-4">
                            <input class="form-control mb-2" type="text" placeholder="Filter Contacts" id="txtFilterContacts">
                        </div>
                    </div>
                </div>

                @if (Model.Contacts.Any())
                {
                    <table class="table table-striped table-bordered table-hover">
                        <tbody>
                            @foreach (var contact in Model.Contacts.OrderBy(l => l.LastName))
                            {
                                <tr data-filter-value="@contact.FirstName @contact.LastName">
                                    <td>
                                        <div class="media align-items-center">
                                            <div class="media-body d-flex">
                                                <div>
                                                    <h4>
                                                        @contact.LastName, @contact.FirstName
                                                    </h4>
                                                    <ul class="list-unstyled m-0">
                                                        @if (!string.IsNullOrWhiteSpace(contact.Email))
                                                        {
                                                            <li>
                                                                <em class="fa fa-envelope fa-fw mr-3"></em>
                                                                <a href="mailto:@contact.Email">@contact.Email</a>
                                                            </li>
                                                        }
                                                        @if (!string.IsNullOrWhiteSpace(contact.MobilePhone))
                                                        {
                                                            <li>
                                                                <em class="fa fa-mobile fa-fw mr-3"></em>
                                                                <a href="tel:@contact.MobilePhone">@contact.MobilePhone</a>
                                                            </li>
                                                        }
                                                        @if (!string.IsNullOrWhiteSpace(contact.OfficePhone))
                                                        {
                                                            <li>
                                                                <em class="fa fa-phone fa-fw mr-3"></em>
                                                                <a href="tel:@contact.OfficePhone">@contact.OfficePhone</a>
                                                            </li>
                                                        }
                                                    </ul>
                                                </div>
                                                <div class="ml-auto">
                                                    <edit action="@Urls.ClientContact(Model.Client.Id, contact.Id).AddEditMode()"></edit>
                                                    <delete action="@Urls.ClientContactDelete(Model.Client.Id, contact.Id)"
                                                            item-name="contact @contact.FirstName, @contact.LastName"></delete>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else
                {
                    <p>No contacts found</p>
                }
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Custom layout for content that needs to fill height with proper padding */
        .layout-content-fill .wrapper .section-container {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
            margin-bottom: 0 !important; /* Override the default footer margin */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            /* Keep the normal padding for readability */
        }

        /* Make the container-fluid fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Prevent spillover */
        }

        /* Make the col-xl-9 from layout fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Allow flexbox to shrink */
        }

        /* Target the row inside col-xl-9 (from ClientContacts content) */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 > .row {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Allow flexbox to shrink */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 > .row > .col {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Allow flexbox to shrink */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 > .row > .col > .card {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Allow flexbox to shrink */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 > .row > .col > .card > .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Allow flexbox to shrink */
            overflow: auto; /* Allow scrolling if content is too tall */
        }
    </style>
}


@section scripts{
    <script>
        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Contacts' }]);
        });

    </script>
}
