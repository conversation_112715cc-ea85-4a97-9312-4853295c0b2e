﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetDeletedPropertiesPagedQuery : PagedQuery<DeletedPropertyModel>
    {
        public GetDeletedPropertiesPagedQuery(
            string searchText,
            int? pageNum = 1,
            int? pageSize = 20) : base(pageNum, pageSize)
        {
            SearchText = searchText;
        }

        public string SearchText { get; }
    }

    public class GetArchivedPropertiesHandler : DapperRequestHandler<GetDeletedPropertiesPagedQuery, PagedDtoSet<DeletedPropertyModel>>
    {
        public GetArchivedPropertiesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<DeletedPropertyModel>> OnHandleAsync(IDbHelper db, GetDeletedPropertiesPagedQuery request)
        {
            var skip = request.PageSize * request.Page;
            var sql = $"SELECT {TableNames.Properties}.*, " +
                $"   {nameof(DeletedPropertyModel.ClientName)}, " +
                $"   {nameof(DeletedPropertyModel.SiteName)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Id)}, SiteAddress.{nameof(AddressDto.Id)}) AS pkAddressId, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.StreetName)}, SiteAddress.{nameof(AddressDto.StreetName)}) AS {nameof(AddressDto.StreetName)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Town)}, SiteAddress.{nameof(AddressDto.Town)}) AS {nameof(AddressDto.Town)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.City)}, SiteAddress.{nameof(AddressDto.City)}) AS {nameof(AddressDto.City)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.County)}, SiteAddress.{nameof(AddressDto.County)}) AS {nameof(AddressDto.County)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Postcode)}, SiteAddress.{nameof(AddressDto.Postcode)}) AS {nameof(AddressDto.Postcode)}, " +
                $"   COALESCE(PropertyAddress.{nameof(AddressDto.Country)}, SiteAddress.{nameof(AddressDto.Country)}) AS {nameof(AddressDto.Country)} " +
                $"FROM {TableNames.Properties} " +
                $"JOIN {TableNames.Clients} ON {nameof(DeletedPropertyModel.ClientId)} = {TableNames.Clients}.{nameof(ClientDto.Id)} " +
                $"LEFT JOIN {TableNames.Addresses} PropertyAddress ON {TableNames.Properties}.{nameof(DeletedPropertyModel.AddressId)} = PropertyAddress.{nameof(AddressDto.Id)} " +
                $"LEFT JOIN {TableNames.Sites} ON {nameof(DeletedPropertyModel.SiteId)} = {TableNames.Sites}.{nameof(SiteDtoExtended.Id)} " +
                $"LEFT JOIN {TableNames.Addresses} SiteAddress ON {TableNames.Sites}.{nameof(SiteDtoExtended.AddressId)} = SiteAddress.{nameof(AddressDto.Id)} " +
                $"WHERE {TableNames.Properties}.Deleted IS NOT NULL " +
                $"ORDER BY COALESCE(SiteName, '') + COALESCE(PropertyCode, '') + COALESCE({TableNames.Properties}.BuildingName, '') + " +
                $"COALESCE({TableNames.Properties}.Unit, '') + COALESCE({TableNames.Properties}.Floor, '') + " +
                $"COALESCE({TableNames.Properties}.BuildingNumber, '') + COALESCE(PropertyAddress.StreetName, SiteAddress.StreetName, '') + " +
                $"COALESCE(PropertyAddress.Town, SiteAddress.Town, '') + COALESCE(PropertyAddress.City, SiteAddress.City, '') + " +
                $"COALESCE(PropertyAddress.County, SiteAddress.County, '') + COALESCE(PropertyAddress.Postcode, SiteAddress.Postcode, '') + " +
                $"COALESCE(PropertyAddress.Country, SiteAddress.Country, '') " +
                $"OFFSET @Skip ROWS FETCH NEXT @Take ROWS ONLY";

            var items = await db.QueryAsync<DeletedPropertyModel, AddressDto>(
                sql,
                (p, a) => { p.Address = a; return p; },
                "pkAddressId",
                request);

            if (!request.SearchText.IsNullOrWhiteSpace())
            {
                items = items.Where(i => i.GetDisplayText().Contains(request.SearchText, StringComparison.OrdinalIgnoreCase));
            }

            var total = await db.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM tblProperties WHERE Deleted IS NOT NULL");

            return PagedDtoSet.From(items, request.Page, request.PageSize, total);
        }
    }
}
