﻿@page "/clients/{clientId:guid}/sites/{siteId:guid}/plans"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitePlansModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutSite";
    ViewData["Client"] = Model.Client;
    ViewData["Site"] = Model.Site;
}

<div class="row" id="divLocations">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">SITE PLANS</small>
            </div>
            <div class="card-body">
                <div class="row">
                    @if (Model.Plans.Any())
                    {
                        <table class="table table-striped table-bordered table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th></th>
                                    <th>Notes</th>
                                    <th style="width: 20px;"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var plan in Model.Plans)
                                {
                                    var url = await Model.GetPlanUrl(plan);
                                    var thumbnailUrl = plan.ThumbnailName.IsEmpty() ? "/images/no-preview.jpg" : await Model.GetPlanThumbnailUrl(plan);

                                    <tr style="cursor: pointer;"
                                        onclick="window.location.href='@Urls.ClientSitePlan(Model.Client.Id, Model.Site.Id, plan.Id).AddEditMode()';">
                                        <td>
                                            <a class="text-muted mr-1" href="@url" title="Download"><img src="@thumbnailUrl" height="100" /></a>
                                            <a class="text-muted mr-1" href="@url" title="Download"><em class="fa fa-download fa-fw"></em></a>
                                        </td>
                                        <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px;">
                                            @plan.Notes
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <delete action="@Urls.ClientSitePlanDelete(Model.Client.Id, Model.Site.Id, plan.Id)" item-name="site plan"></delete>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    }
                    else
                    {
                        <div class="col">
                            <p>No site plans uploaded</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts{
    <script>

        $(() => {

            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                { url: '@Urls.ClientSite(Model.Client.Id, Model.Site.Id)', text: '@Model.Site.GetDisplayText()' },
                { text: 'Plans' },
            ]);
        });

    </script>
}