@inject Hca.WebHost.Pipeline.ViewManager ViewManager
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="description" content="Bootstrap Admin App">
    <meta name="keywords" content="app, responsive, jquery, bootstrap, dashboard, admin">
    <title>@ViewData["Title"] - Spotlite Compliance</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Place styles for plugins -->
    @if (IsSectionDefined("Styles"))
    {
        @RenderSection("Styles", required: false)
    }

    <environment names="Development">
        <!-- =============== VENDOR STYLES ===============-->
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/brands.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/regular.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/solid.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/fontawesome.css' rel="stylesheet" />
        <link href="~/vendor/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" />
        <link href="~/vendor/animate.css/animate.css" rel="stylesheet" />
        <link href="~/vendor/whirl/dist/whirl.css" rel="stylesheet" />
        <link href="~/vendor/spinkit/css/spinkit.css" rel="stylesheet" />
        <link href="~/vendor/dropzone/dist/basic.css" rel="stylesheet" />
        <link href="~/vendor/dropzone/dist/dropzone.css" rel="stylesheet" />
        <link href="~/vendor/select2/dist/css/select2.css" rel="stylesheet" />
        <link href="~/vendor/@@ttskch/select2-bootstrap4-theme/dist/select2-bootstrap4.css" rel="stylesheet">

        <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
        <!-- =============== BOOTSTRAP STYLES ===============-->
        <link href="~/css/bootstrap.css" rel="stylesheet" id="bscss" />
        <!-- =============== APP STYLES ===============-->
        <link href="~/css/app.css" rel="stylesheet" asp-append-version="true" id="maincss" />
    </environment>
    <environment names="Staging,Production">
        <!-- =============== VENDOR STYLES ===============-->
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/brands.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/regular.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/solid.css' rel="stylesheet" />
        <link href='~/vendor/@@fortawesome/fontawesome-free/css/fontawesome.css' rel="stylesheet" />
        <link href="~/vendor/simple-line-icons/css/simple-line-icons.css" rel="stylesheet" />
        <link href="~/vendor/animate.css/animate.css" rel="stylesheet" />
        <link href="~/vendor/whirl/dist/whirl.css" rel="stylesheet" />
        <link href="~/vendor/spinkit/css/spinkit.css" rel="stylesheet" />
        <link href="~/vendor/dropzone/dist/basic.css" rel="stylesheet" />
        <link href="~/vendor/dropzone/dist/dropzone.css" rel="stylesheet" />
        <link href="~/vendor/select2/dist/css/select2.css" rel="stylesheet" />
        <link href="~/vendor/@@ttskch/select2-bootstrap4-theme/dist/select2-bootstrap4.css" rel="stylesheet">

        <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
        <!-- =============== BOOTSTRAP STYLES ===============-->
        <link href="~/css/bootstrap.css" rel="stylesheet" id="bscss" />
        <!-- =============== APP STYLES ===============-->
        <link href="~/css/app.css" rel="stylesheet" asp-append-version="true" id="maincss" />
    </environment>

    <style>
        .input-validation-error {
            border-color: #f05050 !important
        }

        .table-striped > tbody > tr:nth-child(odd) > td,
        .table-striped > tbody > tr:nth-child(odd) > th {
            background-color: #EDEEEF;
        }
    </style>

    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css" />

    <script>
        let to = (promise) => {
            return promise.then(data => [data, null], (err1, err2, err3) => [null, err1, err2, err3]);
        }</script>

    <link id="autoloaded-stylesheet" rel="stylesheet" href="/css/theme-b.css">

    <script src="~/vendor/jquery/dist/jquery.js"></script>

    <script src="https://unpkg.com/htmx.org@1.8.4" integrity="sha384-wg5Y/JwF7VxGk4zLsJEcAojRtlVp1FKKdGy1qN+OMtdq72WRvX/EdRdqg/LOhYeV" crossorigin="anonymous"></script>
</head>
<body>

    @RenderBody()

    @if (IsSectionDefined("BodyArea"))
    {
        @RenderSection("BodyArea", required: false)
    }

    <!-- used for image popups across the site -->
    <div id="imageModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" style="max-width: 60%; width: fit-content;">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <img class="img-fluid" onclick="shareImage(this.src)">
                </div>
            </div>
        </div>
    </div>

    <environment names="Development">

        <!-- =============== VENDOR SCRIPTS ===============-->

        <script src="~/vendor/modernizr/modernizr.custom.js"></script>
        <script src="~/vendor/js-storage/js.storage.js"></script>
        <script src="~/vendor/screenfull/dist/screenfull.js"></script>
        <script src="~/vendor/i18next/i18next.js"></script>
        <script src="~/vendor/i18next-xhr-backend/i18nextXHRBackend.js"></script>

        @*<script src="~/vendor/jquery/dist/jquery.js"></script>*@
        <script src="~/vendor/popper.js/dist/umd/popper.js"></script>
        <script src="~/vendor/bootstrap/dist/js/bootstrap.js"></script>

        <script src="~/vendor/jquery-steps/build/jquery.steps.min.js"></script>
        <script src="~/vendor/dropzone/dist/dropzone.js"></script>
        <script src="~/vendor/select2/dist/js/select2.full.js"></script>

        @*<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>*@
        <!-- =============== PAGE VENDOR SCRIPTS ===============-->
        @RenderSection("scripts", required: false)

        <!-- =============== APP SCRIPTS ===============-->
        <!-- Wrapper -->
        <script src="~/master/js/modules/common/wrapper.js"></script>
        <!-- App init -->
        <script src="~/master/js/app.init.js"></script>
        <!-- Modules -->
        <script src="~/master/js/modules/common/bootstrap-start.js"></script>
        <script src="~/master/js/modules/common/card-tools.js"></script>
        <script src="~/master/js/modules/common/constants.js"></script>
        <script src="~/master/js/modules/common/fullscreen.js"></script>
        <script src="~/master/js/modules/common/load-css.js"></script>
        <script src="~/master/js/modules/common/localize.js"></script>
        <script src="~/master/js/modules/common/navbar-search.js"></script>
        <script src="~/master/js/modules/common/now.js"></script>
        <script src="~/master/js/modules/common/rtl.js"></script>
        <script src="~/master/js/modules/common/sidebar.js"></script>
        <script src="~/master/js/modules/common/slimscroll.js"></script>
        <script src="~/master/js/modules/common/table-checkall.js"></script>
        <script src="~/master/js/modules/common/toggle-state.js"></script>
        <script src="~/master/js/modules/common/trigger-resize.js"></script>
        <script src="~/master/js/modules/elements/sortable.js"></script>
        <script src="~/master/js/modules/forms/forms.js"></script>
        <!-- Custom -->
        <script src="~/master/js/custom/custom.js"></script>

    </environment>

    <environment names="Staging,Production">

        <!-- =============== VENDOR SCRIPTS ===============-->

        <script src="~/vendor/modernizr/modernizr.custom.js"></script>
        <script src="~/vendor/js-storage/js.storage.js"></script>
        <script src="~/vendor/screenfull/dist/screenfull.js"></script>
        <script src="~/vendor/i18next/i18next.js"></script>
        <script src="~/vendor/i18next-xhr-backend/i18nextXHRBackend.js"></script>
        @*<script src="~/vendor/jquery/dist/jquery.js"></script>*@
        <script src="~/vendor/popper.js/dist/umd/popper.js"></script>
        <script src="~/vendor/bootstrap/dist/js/bootstrap.js"></script>
        <script src="~/vendor/jquery-steps/build/jquery.steps.min.js"></script>
        <script src="~/vendor/dropzone/dist/dropzone.js"></script>
        <script src="~/vendor/select2/dist/js/select2.full.js"></script>

        @*<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>*@
        <!-- =============== PAGE VENDOR SCRIPTS ===============-->
        @RenderSection("scripts", required: false)

        <!-- =============== APP SCRIPTS ===============-->

        <script src="~/js/app.js"></script>

    </environment>
    <script>Dropzone.autoDiscover = false;</script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <script src="~/vendor/html5sortable/dist/html5sortable.js"></script>
    <script src="~/vendor/sweetalert/dist/sweetalert.min.js"></script>

    <script src="~/master/js/custom/file-drop/file-drop.js"></script>
    <link href="~/master/js/custom/file-drop/file-drop.css" rel="stylesheet" />

    <script>
        function drawBreadcrumb(items) {
            var olBreadcrumb = $('ol.breadcrumb');
            $.each(items, (i, item) => {
                var li = $('<li></li>').addClass('breadcrumb-item');
                if (i === items.length - 1) {
                    li.addClass('active').html(item.text).val();
                    olBreadcrumb.append(li);
                    return;
                }
                var a = $('<a></a>').attr({ href: item.url }).text(decodeURI($('<div/>').html(item.text).text()));
                olBreadcrumb.append(li.append(a));
            });
        }

        function shareImage(imageUrl) {
            if (navigator.share) {
                navigator.share({
                    url: imageUrl
                })
                    .then(() => console.log('Image shared successfully'))
                    .catch((error) => console.error('Error sharing image:', error));
            }
        }
    </script>

    <partial name="_ValidationScriptsPartial" />

</body>
</html>

