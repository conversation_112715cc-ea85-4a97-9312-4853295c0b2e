/* 
 * Layout Client Fill CSS
 * Provides consistent height-filling behavior for pages using _LayoutClient layout
 * Prevents content spillover and ensures proper viewport containment
 */

/* Base layout structure */
.layout-content-fill .wrapper .section-container {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
    margin-bottom: 0 !important; /* Override the default footer margin */
}

.layout-content-fill .wrapper .section-container .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    /* Keep the normal padding for readability */
}

/* Make the container-fluid fill available space */
.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevent spillover */
}

/* Generic row and column handling */
.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col > .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col > .card > .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
    overflow: auto; /* Allow scrolling if content is too tall */
}

/* Specific handling for pages with search forms and results */
.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .col-xl-9 {
    flex-shrink: 0; /* Don't shrink the search form */
}

/* Make the results row fill remaining space */
.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col > .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col > .card > .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
    overflow: auto; /* Allow scrolling if content is too tall */
}

/* Form-specific handling for Client details page */
.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > form {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > form > .row {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > form > .row > .col {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > form > .row > .col > .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > form > .row > .col > .card > .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
    overflow: auto; /* Allow scrolling if content is too tall */
}

/* Content area handling for Properties page */
.layout-content-fill .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .content-area > .row:first-child {
    flex-shrink: 0; /* Don't shrink the search form */
}

.layout-content-fill .content-area > .row:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

/* Results card handling */
.layout-content-fill .results-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .results-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
    overflow: auto; /* Allow scrolling if content is too tall */
}

.layout-content-fill .results-card .card-body > .row {
    flex: 1;
    display: flex;
    margin: 0; /* Remove default row margins */
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .results-card .card-body > .row > .col-lg-6 {
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .results-card .card-body > .row > .col-lg-6:first-child {
    overflow-y: auto; /* Allow scrolling in the property list column */
}

.layout-content-fill .property-list-table {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #dee2e6; /* Add border for visual clarity */
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .results-card .card-body .col-lg-6:first-child {
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flexbox to shrink */
}

.layout-content-fill .results-card .card-body .col-lg-6:first-child > br,
.layout-content-fill .results-card .card-body .col-lg-6:first-child > small {
    flex-shrink: 0; /* Don't let these elements shrink */
}

.layout-content-fill .results-card .card-body > .row > .col-lg-6:last-child {
    min-height: 400px; /* Minimum height for the map */
}
