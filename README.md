# hca

DB Creation Script:


drop table clients

create table Clients (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Clients_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Clients_Created] DEFAULT (getdate()) NOT NULL,
    ClientName NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_Clients] PRIMARY KEY CLUSTERED ([Id] ASC),
)

insert into clients (ClientName) values ('Client 1')
insert into clients (ClientName) values ('Client 2')
insert into clients (ClientName) values ('Client 3')

-- drop table inspections

-- drop table Sites

create table Sites (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Sites_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Sites_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    SiteName NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_Sites] PRIMARY KEY CLUSTERED ([Id] ASCI),
    CONSTRAINT [FK_Sites_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([Id])
)

create table Inspections (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Inspection_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Inspections_Created] DEFAULT (getdate()) NOT NULL,
    SiteId UNIQUEIDENTIFIER NOT NULL,
    InspectionType INT NOT NULL,
    CONSTRAINT [PK_Inspections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Inspections_Sites] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id])
)

-- create table SectionTypes (
--     Id INT NOT NULL,
--     SectionTypeName NVARCHAR(MAX) NOT NULL,
--     CONSTRAINT [PK_SectionTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
-- )

create table Templates (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Templates_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Templates_Created] DEFAULT (getdate()) NOT NULL,
    TemplateName NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_Templates] PRIMARY KEY CLUSTERED ([Id] ASC),
)
 
-- drop table TemplateFields
-- drop table templatesections

create table TemplateSections (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_TemplateSections_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_TemplateSections_Created] DEFAULT (getdate()) NOT NULL,
    TemplateId UNIQUEIDENTIFIER NOT NULL,
    SectionType INT NOT NULL,
    SectionName NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_TemplateSections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TemplateSections_Templates] FOREIGN KEY ([TemplateId]) REFERENCES [dbo].[Templates] ([Id])
)

create table TemplateFields (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_TemplateFields_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_TemplateFields_Created] DEFAULT (getdate()) NOT NULL,
    TemplateSectionId UNIQUEIDENTIFIER NOT NULL,
    FieldType INT NOT NULL,
    CONSTRAINT [PK_TemplateFields] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TemplateFields_TemplateSections] FOREIGN KEY ([TemplateSectionId]) REFERENCES [dbo].[TemplateSections] ([Id])
)


-- CONSTRAINT [FK_Templates] FOREIGN KEY ([TemplateId]) REFERENCES [dbo].[Templates] ([Id])

-- INT  IDENTITY (1, 1) NOT NULL

-- UNIQUEIDENTIFIER CONSTRAINT [DF_SectionTypes_Id] DEFAULT (newid()) NOT NULL,

-- Created DATETIME CONSTRAINT [DF_Clients_Created] DEFAULT (getdate()) NOT NULL,

