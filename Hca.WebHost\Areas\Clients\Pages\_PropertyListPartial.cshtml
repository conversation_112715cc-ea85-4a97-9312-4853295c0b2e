@model IEnumerable<Hca.Lib.Features.Clients.PropertyDtoExtended>
@using Hca.Lib.Features.Clients

@{
    string NextInspectionDateColour(DateTime? nextInspectionDate)
    {
        return nextInspectionDate switch
        {
            null => "#768294",
            DateTime dt when dt < DateTime.Now => "#f05050",
            DateTime dt when dt < DateTime.Now.AddMonths(1) => "#ff902b",
            _ => "#27c24c",
        };
    }
}

@if (Model.Any())
{
    <div class="table-container fixed-header-table property-list-table">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th style="width: 80%;">Address</th>
                    <th style="width: 20%;">Next Inspection Due</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var location in Model)
                {
                    <tr style="cursor: pointer;"
                        data-filter-value="@location.PropertyCode"
                        data-toggle="tooltip"
                        data-placement="top"
                        data-html="true"
                        data-original-title="@(location.ArchiveReason.IsPopulated() ? $"<strong>Archive Reason</strong>: {location.ArchiveReason}" : "")"
                        onclick="window.location.href='@Urls.Properties/@location.Id';">
                        <td style="width: 80%">@location.GetDisplayText()</td>
                        <td style="font-weight: bolder; color: @NextInspectionDateColour(location.NextInspection); text-align: center; width: 20%;">
                            @(location.NextInspection?.ToString("dd-MM-yyyy"))
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}
else
{
    <p>No properties found</p>
}

<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });
</script>