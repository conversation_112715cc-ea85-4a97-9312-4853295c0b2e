﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class UpdateProperty : ICommand
    {
        public UpdateProperty(
            Guid clientId,
            Guid propertyId,
            Guid? addressId,
            Guid? siteId,
            string code)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            AddressId = addressId;
            SiteId = siteId;
            Code = code;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public Guid? AddressId { get; }
        public Guid? SiteId { get; }
        public string Code { get; }
    }

    public class UpdatePropertyHandler : DapperRequestHandler<UpdateProperty, CommandResult>
    {
        public UpdatePropertyHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateProperty request)
        {
            var property = await db.GetAsync<PropertyDto>(request.PropertyId);

            if (request.SiteId.HasValue) property.SiteId = request.SiteId;
            property.PropertyCode = request.Code;
            property.AddressId = request.AddressId;

            await db.UpdateAsync(property);

            return CommandResult.Success();
        }
    }
}
