﻿@page "/clients/{clientId:guid}/properties"
@model Hca.WebHost.Areas.Clients.Pages.ClientPropertiesModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="client-properties-container">
    <div class="search-section">
        <div class="row">
            <div class="col">
                <form hx-post="/clients/@Model.Client.Id/properties"
                      hx-target="#divPropertySearchResults"
                      hx-swap="innerHTML"
                      hx-trigger="load, change from:#ShowArchivedProperties, click from:#btnPropertiesSearch">
                    <div class="form-group mb-4">
                        <input class="form-control mb-2" type="text" placeholder="Search properties" id="txtSearch" name="searchText">
                        <div class="d-flex">
                            <button class="btn btn-secondary" type="button" id="btnPropertiesSearch">
                                Search
                            </button>
                            <button class="btn btn-sm btn-secondary">Clear</button>
                            <div class="d-flex align-items-center">
                                &nbsp;&nbsp;&nbsp;
                                <select asp-for="ComplianceStatusFilter">
                                    <option value="0">Show All</option>
                                    <option value="1">Compliant</option>
                                    <option value="2">Not compliant</option>
                                    <option value="3">Next inspection is due within one month</option>
                                    <option value="4">No documents uploaded and/or no ACMs identified or presumed</option>
                                </select>
                                &nbsp;&nbsp;&nbsp;
                                <input asp-for="ShowArchivedProperties" class="ml-4 mx-2" />Show Archived Properties
                            </div>
                        </div>
                        @Html.AntiForgeryToken()
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="results-section">
        <div class="row">
            <div class="col">
                <div class="card card-default">
                    <div class="card-header">
                        <small class="text-muted">PROPERTIES</small>
                    </div>
                    <div class="card-body" id="divPropertySearchResults">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Make the content area fill available height */
        .client-properties-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 55px - 60px - 40px); /* viewport - navbar - footer - padding adjustments */
        }

        .client-properties-container .search-section {
            flex-shrink: 0; /* Don't shrink the search area */
        }

        .client-properties-container .results-section {
            flex: 1; /* Take up remaining space */
            display: flex;
            flex-direction: column;
        }

        .client-properties-container .results-section .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .client-properties-container .results-section .card-body {
            flex: 1;
            overflow-y: auto; /* Allow scrolling within the results area */
            display: flex;
            flex-direction: column;
        }

        /* Ensure dynamically loaded content fills available height */
        .client-properties-container .results-section .card-body #divPropertySearchResults {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the search results row fill available space */
        .client-properties-container .results-section .card-body #divPropertySearchResults > .row {
            flex: 1;
            display: flex;
            margin: 0;
        }

        /* Make the columns fill available space */
        .client-properties-container .results-section .card-body #divPropertySearchResults > .row > .col-lg-6 {
            display: flex;
            flex-direction: column;
        }

        /* Override the fixed max-height on the table container */
        .client-properties-container .results-section .card-body #divPropertySearchResults .table-container.fixed-header-table {
            max-height: none !important;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the table fill the container */
        .client-properties-container .results-section .card-body #divPropertySearchResults .table-container.fixed-header-table table {
            flex: 1;
            margin-bottom: 0;
        }

        /* Ensure table body scrolls within the available space */
        .client-properties-container .results-section .card-body #divPropertySearchResults .table-container.fixed-header-table table tbody {
            display: block;
            overflow-y: auto;
            flex: 1;
        }

        .client-properties-container .results-section .card-body #divPropertySearchResults .table-container.fixed-header-table table thead,
        .client-properties-container .results-section .card-body #divPropertySearchResults .table-container.fixed-header-table table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }
    </style>
}

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Properties' }]);
        });

    </script>
}
