@page "/clients/{clientId:guid}/sites/{id}/properties"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitePropertiesModel
@using Hca.Lib.Features.Clients
@using Hca.Lib.Features.Clients.Queries
@{
    var breadcrumbName = "Properties";
    var heading = breadcrumbName = "Properties";

    Layout = "_LayoutSite";
    ViewData["Client"] = Model.Client;
    ViewData["Site"] = Model.Site;

    string NextInspectionDateColour(DateTime? nextInspectionDate)
    {
        return nextInspectionDate switch
        {
            null => "#768294",
            DateTime dt when dt < DateTime.Now => "#f05050",
            DateTime dt when dt < DateTime.Now.AddMonths(1) => "#ff902b",
            _ => "#27c24c",
        };
    }

    object AugnentProperty(PropertyDtoExtended property)
    {
        return new
        {
            property.Id,
            property.PropertyCode,
            property.ArchiveReason,
            nextInspection = property.NextInspection?.ToString("dd-MM-yyyy"),
            displayText = property.GetDisplayText(),
            status = property.NextInspection switch
            {
                null => ComplianceStatus.NoDocumentsOrAcms,
                DateTime dt when dt < DateTime.Now => ComplianceStatus.NotCompliant,
                DateTime dt when dt < DateTime.Now.AddMonths(1) => ComplianceStatus.DueWithinOneMonth,
                _ => ComplianceStatus.Compliant,
            },
            nextInspectionColour = NextInspectionDateColour(property.NextInspection),
        };
    }
}

<div class="row" id="divLocations">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">PROPERTIES</small>
            </div>
            <div class="card-body">
                @if (Model.Properties.Any())
                {
                    <div class="table-container fixed-header-table" style="max-height: 500px;">
                        <p>
                            <select id="selComplianceStatus">
                                <option value="0">Show All</option>
                                <option value="1">Compliant</option>
                                <option value="2">Not compliant</option>
                                <option value="3">Next inspection is due within one month</option>
                                <option value="4">No documents uploaded and/or no ACMs identified or presumed</option>
                            </select>
                        </p>
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th style="width: 80%;">Address</th>
                                    <th style="width: 20%;">Next Inspection Due</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>

                    <script>
                        var properties = @Html.Raw(Json.Serialize(Model.Properties.Select(p => AugnentProperty(p))));

                        function drawProperties(complianceStatus) {
                            var filteredProperties = properties;

                            if (complianceStatus) {
                                filteredProperties = filteredProperties.filter(function (property) {
                                    return property.status === complianceStatus;
                                });
                            }

                            filteredProperties.forEach(function (property) {
                                var tbody = document.querySelector("tbody");
                                var tr = document.createElement("tr");
                                tr.style.cursor = "pointer";
                                tr.setAttribute("data-filter-value", property.propertyCode);
                                tr.setAttribute("data-toggle", "tooltip");
                                tr.setAttribute("data-placement", "top");
                                tr.setAttribute("data-html", "true");
                                tr.setAttribute("data-original-title", property.archiveReason ? "<strong>Archive Reason</strong>: " + property.archiveReason : "");
                                tr.onclick = function () {
                                    window.location.href = '@Urls.Properties/' + property.id;
                                };

                                var tdAddress = document.createElement("td");
                                tdAddress.style.width = "80%";
                                tdAddress.textContent = property.displayText;
                                tr.appendChild(tdAddress);

                                var tdNextInspection = document.createElement("td");
                                tdNextInspection.style.fontWeight = "bolder";
                                tdNextInspection.style.color = property.nextInspectionColour;
                                tdNextInspection.style.textAlign = "center";
                                tdNextInspection.style.width = "20%";
                                tdNextInspection.textContent = property.nextInspection;
                                tr.appendChild(tdNextInspection);

                                tbody.appendChild(tr);
                            });

                            $('[data-toggle="tooltip"]').tooltip({
                                container: 'body'
                            });
                        }

                        function drawFilteredProperties(complianceStatus) {
                            var tbody = document.querySelector("tbody");
                            tbody.innerHTML = "";
                            drawProperties(complianceStatus);
                        }

                        $('#selComplianceStatus').on('change', (e) => {
                            var complianceStatus = parseInt(e.target.value);
                            if (complianceStatus === 0) {
                                drawFilteredProperties();
                            } else {
                                drawFilteredProperties(complianceStatus);
                            }
                        });

                        $(() => drawProperties());
                    </script>
                }
                else
                {
                    <p>No properties found</p>
                }
            </div>
        </div>
    </div>
</div>

@section scripts{
    @*<partial name="_PropertyListMapScripts" model="(Model.Client, Model.Properties)" />*@

    <script>
        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                { url: '@Urls.ClientSite(Model.Client.Id, Model.Site.Id)', text: '@Model.Site.GetDisplayText()' },
                { text: '@breadcrumbName' },
            ]);
    });

    </script>
}