﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class AddPropertyToClient : ICommand
    {
        public AddPropertyToClient(
            Guid clientId,
            Guid propertyId,
            string code,
            string floor,
            string unit,
            string buildingName,
            string buildingNumber)
            : this(
                 clientId,
                 propertyId,
                 code,
                 floor,
                 unit,
                 buildingName,
                 buildingNumber,
                 null)
        {
        }

        public AddPropertyToClient(
            Guid clientId,
            Guid propertyId,
            string code,
            string floor,
            string unit,
            string buildingName,
            string buildingNumber,
            Guid? siteId)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            Code = code;
            SiteId = siteId;
            Floor = floor;
            Unit = unit;
            BuildingName = buildingName;
            BuildingNumber = buildingNumber;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public string Code { get; }
        public string Floor { get; }
        public string Unit { get; }
        public string BuildingName { get; }
        public string BuildingNumber { get; }
        public Guid? SiteId { get; }
    }

    public class AddPropertyToCLientHandler : DapperRequestHandler<AddPropertyToClient, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;
        private readonly SiteCountsService _siteCountsService;

        public AddPropertyToCLientHandler(IDbHelper dbHelper, ClientCountsService clientCountsService, SiteCountsService siteCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
            _siteCountsService = siteCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddPropertyToClient request)
        {
            await db.InsertAsync(new PropertyDtoExtended
            {
                ClientId = request.ClientId,
                Id = request.PropertyId,
                PropertyCode = request.Code,
                SiteId = request.SiteId,
                Floor = request.Floor,
                Unit = request.Unit,
                BuildingName = request.BuildingName,
                BuildingNumber = request.BuildingNumber,
            });

            _clientCountsService.ClearClientCountsAsync(request.ClientId);
            if (request.SiteId.HasValue)
            {
                _siteCountsService.ClearSiteCountsAsync(request.SiteId.Value);
            }

            return CommandResult.Success();
        }
    }
}
