﻿@using Hca.Lib.Features.Clients;
@model ClientPropertiesModel

<div class="row" id="divLocationFilter" style="display: none;">
    <div class="col">
        <div class="form-group mb-4">
            <input class="form-control mb-2" type="text" placeholder="Filter Properties">
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-6">
        <partial name="_PropertyListPartial" model="Model.Properties" />

        @if (Model.ShowArchivedProperties)
        {
            <br />
            <small class="text-muted">ARCHIVED PROPERTIES</small>
            <br />

            <br />

            @if (Model.ArchivedProperties.Any())
            {
                <partial name="_PropertyListPartial" model="Model.ArchivedProperties" />
            }
            else
            {
                <p>No archived properties found matching the current criteria</p>
            }
        }
    </div>
    <div class="col-lg-6">
        <partial name="_Map" model="new _MapModel()" />
    </div>
</div>

<partial name="_ListMapScripts" model="new _ListMapScriptsModel(Model.Client, Model.Properties)" />
